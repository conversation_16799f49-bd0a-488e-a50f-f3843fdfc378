/**
 * 页面内容提取工具函数
 * 用于在侧边栏中通过 chrome.scripting.executeScript 提取页面内容
 */

export interface PageContentResult {
  title: string
  content: string
  url: string
  textLength: number
}

/**
 * 提取页面主要文本内容的函数
 * 这个函数会被注入到页面中执行
 */
export const extractPageContentScript = (): PageContentResult => {
  // 获取页面标题
  const title = document.title || '未知页面'
  const url = window.location.href

  // 需要忽略的标签
  const ignoreTags = new Set([
    'SCRIPT', 'STYLE', 'NOSCRIPT', 'IFRAME', 'OBJECT', 'EMBED',
    'AUDIO', 'VIDEO', 'CANVAS', 'SVG', 'MATH'
  ])

  // 需要忽略的类名和ID（常见的导航、广告、侧边栏等）
  const ignoreSelectors = [
    '.nav', '.navigation', '.menu', '.sidebar', '.footer', '.header',
    '.advertisement', '.ads', '.banner', '.popup', '.modal',
    '.breadcrumb', '.pagination', '.social', '.share',
    '#nav', '#navigation', '#menu', '#sidebar', '#footer', '#header',
    '[role="navigation"]', '[role="banner"]', '[role="contentinfo"]',
    '[role="complementary"]', '[aria-hidden="true"]'
  ]

  // 检查元素是否应该被忽略
  const shouldIgnoreElement = (element: Element): boolean => {
    // 检查标签名
    if (ignoreTags.has(element.tagName)) {
      return true
    }

    // 检查是否隐藏
    const style = window.getComputedStyle(element)
    if (style.display === 'none' || 
        style.visibility === 'hidden' || 
        style.opacity === '0' ||
        element.hasAttribute('hidden')) {
      return true
    }

    // 检查选择器
    for (const selector of ignoreSelectors) {
      try {
        if (element.matches(selector)) {
          return true
        }
      } catch (e) {
        // 忽略无效的选择器
      }
    }

    return false
  }

  // 提取文本内容
  const extractTextFromElement = (element: Element): string => {
    if (shouldIgnoreElement(element)) {
      return ''
    }

    let text = ''
    
    // 遍历子节点
    for (const node of element.childNodes) {
      if (node.nodeType === Node.TEXT_NODE) {
        const textContent = node.textContent?.trim()
        if (textContent && textContent.length > 2) {
          text += textContent + ' '
        }
      } else if (node.nodeType === Node.ELEMENT_NODE) {
        const childElement = node as Element
        if (!shouldIgnoreElement(childElement)) {
          text += extractTextFromElement(childElement)
        }
      }
    }

    return text
  }

  // 优先提取主要内容区域
  const mainContentSelectors = [
    'main', '[role="main"]', '.main', '#main',
    'article', '.article', '.content', '.post',
    '.entry', '.story', '.text', '.body'
  ]

  let content = ''

  // 尝试从主要内容区域提取
  for (const selector of mainContentSelectors) {
    try {
      const elements = document.querySelectorAll(selector)
      for (const element of elements) {
        const elementText = extractTextFromElement(element)
        if (elementText.length > content.length) {
          content = elementText
        }
      }
    } catch (e) {
      // 忽略选择器错误
    }
  }

  // 如果没有找到主要内容，从body提取
  if (!content || content.length < 100) {
    content = extractTextFromElement(document.body)
  }

  // 清理文本
  content = content
    .replace(/\s+/g, ' ') // 合并多个空白字符
    .replace(/\n\s*\n/g, '\n') // 合并多个换行
    .trim()

  // 限制内容长度（避免过长的内容）
  const maxLength = 8000 // 8000字符限制
  if (content.length > maxLength) {
    content = content.substring(0, maxLength) + '...'
  }

  return {
    title,
    content,
    url,
    textLength: content.length
  }
}

/**
 * 在侧边栏中调用的页面内容提取函数
 */
export const extractPageContent = async (tabId: number): Promise<PageContentResult> => {
  try {
    const results = await chrome.scripting.executeScript({
      target: { tabId },
      func: extractPageContentScript
    })

    if (results && results[0] && results[0].result) {
      return results[0].result as PageContentResult
    }

    throw new Error('无法提取页面内容')
  } catch (error) {
    console.error('页面内容提取失败:', error)
    throw error
  }
}
