/**
 * 页面内容提取工具的测试
 */

import { extractPageContentScript } from '../pageContentExtractor'

// Mock DOM environment for testing
const mockDocument = {
  title: 'Test Page Title',
  body: {
    childNodes: [
      {
        nodeType: 3, // TEXT_NODE
        textContent: 'This is main content text.'
      },
      {
        nodeType: 1, // ELEMENT_NODE
        tagName: 'DIV',
        childNodes: [
          {
            nodeType: 3,
            textContent: 'More content here.'
          }
        ],
        matches: () => false,
        hasAttribute: () => false
      }
    ]
  },
  querySelectorAll: (selector: string) => {
    if (selector === 'main' || selector.includes('main')) {
      return [{
        childNodes: [
          {
            nodeType: 3,
            textContent: 'Main section content'
          }
        ]
      }]
    }
    return []
  }
}

const mockWindow = {
  location: {
    href: 'https://example.com/test'
  },
  getComputedStyle: () => ({
    display: 'block',
    visibility: 'visible',
    opacity: '1'
  })
}

// Mock global objects
global.document = mockDocument as any
global.window = mockWindow as any
global.Node = {
  TEXT_NODE: 3,
  ELEMENT_NODE: 1
} as any

describe('pageContentExtractor', () => {
  describe('extractPageContentScript', () => {
    it('should extract basic page information', () => {
      const result = extractPageContentScript()
      
      expect(result.title).toBe('Test Page Title')
      expect(result.url).toBe('https://example.com/test')
      expect(result.content).toBeTruthy()
      expect(result.textLength).toBeGreaterThan(0)
    })

    it('should extract and clean text content', () => {
      const result = extractPageContentScript()
      
      // Should contain some text content
      expect(result.content.length).toBeGreaterThan(0)
      // Should not have excessive whitespace
      expect(result.content).not.toMatch(/\s{3,}/)
    })

    it('should limit content length', () => {
      const result = extractPageContentScript()
      
      // Should not exceed maximum length
      expect(result.content.length).toBeLessThanOrEqual(8003) // 8000 + '...'
    })
  })
})
