/**
 * Chrome Storage 工具函数
 */

// 存储键名常量
export const STORAGE_KEYS = {
  ALWAYS_FLOAT: 'alwaysFloat',
  DISABLED_WEBSITES: 'disabledWebsites',
  SELECTION_ALWAYS_SHOW: 'selectionAlwaysShow',
  SELECTION_MODE: 'selectionMode', // 'read' | 'edit'
  TRANSLATION_DEFAULT_TARGET_LANGUAGE: 'translationDefaultTargetLanguage',
} as const

// 存储数据类型定义
export interface StorageData {
  [STORAGE_KEYS.ALWAYS_FLOAT]: boolean
  [STORAGE_KEYS.DISABLED_WEBSITES]: Array<{
    id: string
    domain: string
  }>
  [STORAGE_KEYS.SELECTION_ALWAYS_SHOW]: boolean
  [STORAGE_KEYS.SELECTION_MODE]: 'read' | 'edit'
  [STORAGE_KEYS.TRANSLATION_DEFAULT_TARGET_LANGUAGE]: string
}

/**
 * 获取存储数据
 * @param keys 要获取的键名数组
 * @returns Promise<Partial<StorageData>>
 */
export const getStorageData = async <K extends keyof StorageData>(
  keys: K[]
): Promise<Pick<StorageData, K>> => {
  try {
    const result = await chrome.storage.sync.get(keys)
    return result as Pick<StorageData, K>
  } catch (error) {
    console.error('获取存储数据失败:', error)
    throw error
  }
}

/**
 * 设置存储数据
 * @param data 要存储的数据对象
 * @returns Promise<void>
 */
export const setStorageData = async (
  data: Partial<StorageData>
): Promise<void> => {
  try {
    await chrome.storage.sync.set(data)
  } catch (error) {
    console.error('设置存储数据失败:', error)
    throw error
  }
}

/**
 * 删除存储数据
 * @param keys 要删除的键名数组
 * @returns Promise<void>
 */
export const removeStorageData = async (keys: string[]): Promise<void> => {
  try {
    await chrome.storage.sync.remove(keys)
  } catch (error) {
    console.error('删除存储数据失败:', error)
    throw error
  }
}

/**
 * 清空所有存储数据
 * @returns Promise<void>
 */
export const clearStorageData = async (): Promise<void> => {
  try {
    await chrome.storage.sync.clear()
  } catch (error) {
    console.error('清空存储数据失败:', error)
    throw error
  }
}

/**
 * 监听存储变化
 * @param callback 变化回调函数
 * @returns 取消监听的函数
 */
export const watchStorageChange = (
  callback: (changes: { [key: string]: chrome.storage.StorageChange }) => void
): (() => void) => {
  const listener = (changes: {
    [key: string]: chrome.storage.StorageChange
  }) => {
    callback(changes)
  }

  chrome.storage.onChanged.addListener(listener)

  // 返回取消监听的函数
  return () => {
    chrome.storage.onChanged.removeListener(listener)
  }
}

/**
 * 获取存储使用情况
 * @returns Promise<number>
 */
export const getStorageInfo = async (): Promise<number> => {
  try {
    return await chrome.storage.sync.getBytesInUse()
  } catch (error) {
    console.error('获取存储信息失败:', error)
    throw error
  }
}

/**
 * 获取所有配置信息
 * @returns Promise<StorageData>
 */
export const getAllConfig = async (): Promise<StorageData> => {
  try {
    const allKeys = Object.values(STORAGE_KEYS)
    const result = await chrome.storage.sync.get(allKeys)
    return result as StorageData
  } catch (error) {
    console.error('获取所有配置失败:', error)
    throw error
  }
}

/**
 * 获取配置信息的摘要
 * @returns Promise<{ [key: string]: any }>
 */
export const getConfigSummary = async (): Promise<{ [key: string]: any }> => {
  try {
    const config = await getAllConfig()
    return {
      alwaysFloat: config[STORAGE_KEYS.ALWAYS_FLOAT],
      disabledWebsitesCount:
        config[STORAGE_KEYS.DISABLED_WEBSITES]?.length || 0,
      selectionAlwaysShow: config[STORAGE_KEYS.SELECTION_ALWAYS_SHOW],
      selectionMode: config[STORAGE_KEYS.SELECTION_MODE],
      translationDefaultTargetLanguage:
        config[STORAGE_KEYS.TRANSLATION_DEFAULT_TARGET_LANGUAGE],
    }
  } catch (error) {
    console.error('获取配置摘要失败:', error)
    throw error
  }
}
