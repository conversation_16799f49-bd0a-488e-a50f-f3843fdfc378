import { useState, useEffect } from 'react'
import { userStorage } from '@src/common/utils'
import type { UserInfo } from '@src/common/utils'

// 检查登录状态的函数，从sidepanel/utils移动过来
const checkLoginStatus = (callback: (isLoggedIn: boolean, data?: UserInfo) => void) => {
  // 由于移除了登录功能，这里返回默认的匿名用户状态
  const defaultUser: UserInfo = {
    id: "anonymous_user",
    name: "匿名用户",
    email: "<EMAIL>"
  }
  callback(true, defaultUser)
}

export const useAuth = () => {
  const [isCompleted, setIsCompleted] = useState(false)

  useEffect(() => {
    // 页面加载时检查登录状态
    checkLoginStatus(async (isLoggedIn, data) => {
      if (isLoggedIn && data) {
        // 保存到本地存储
        await userStorage.setUserInfo(data)
        setIsCompleted(true)
      }
    })

    // 监听来自 background 的登录成功消息
    const messageListener = async (message: any, _sender: any, _sendResponse: any) => {
      if (message.type === 'loginSuccess') {
        checkLoginStatus(async (isLoggedIn, data) => {
          if (isLoggedIn && data) {
            // 保存到本地存储
            await userStorage.setUserInfo(data)
            setIsCompleted(true)
            console.log('登录成功，更新用户信息:', data)
            
            // 关闭登录 tab
            if (message.payload && message.payload.tabId) {
              chrome.tabs.remove(message.payload.tabId)
            }
          }
        })
      }
    }

    chrome.runtime.onMessage.addListener(messageListener)

    // 清理监听器
    return () => {
      chrome.runtime.onMessage.removeListener(messageListener)
    }
  }, [])

  return { isCompleted }
}

export default useAuth
