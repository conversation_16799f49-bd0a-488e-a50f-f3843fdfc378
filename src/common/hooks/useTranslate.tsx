import { useEffect, useCallback } from 'react'
import { message } from 'antd/es'
import { MessageType } from '@src/common/const'
import { ipConnectivity } from '@src/common/utils'
import { log } from '@ht/xlog'
import type { MessageInstance } from 'antd/es/message/interface'

// 统一的useTranslate hook，专注于UI交互模式
interface UseTranslateProps {
  // 可选参数，用于UI交互模式
  chatUiRef?: React.RefObject<any>
  messageApi?: MessageInstance
}

interface UseTranslateReturn {
  // API模式返回的contextHolder
  contextHolder?: React.ReactNode
  // UI交互模式返回的handleTranslate函数
  handleTranslate?: (pageTitle: string) => Promise<void>
}

const useTranslate = (props?: UseTranslateProps): UseTranslateReturn => {
  const [internalMessageApi, contextHolder] = message.useMessage()
  const messageApiToUse = props?.messageApi || internalMessageApi

  // 获取当前标签页
  const getCurrentTab = useCallback(async (messageApi: MessageInstance) => {
    try {
      const tabs = await chrome.tabs.query({ active: true, currentWindow: true })
      return tabs[0]
    } catch (error) {
      console.error('获取当前标签页失败:', error)
      messageApi.error('获取当前标签页失败')
      return null
    }
  }, [])

  // 刷新当前页面
  const refreshCurrentPage = useCallback(async (tabId: number) => {
    try {
      await chrome.tabs.reload(tabId)
    } catch (error) {
      console.error('刷新页面失败:', error)
    }
  }, [])

  // 记录日志
  const reportLog = useCallback((pageTitle: string) => {
    log({
      id: 'button_click',
      page_id: 'quickReplyBtn',
      page_title: pageTitle,
    })
  }, [])

  // UI交互模式的翻译处理函数
  const handleTranslate = useCallback(async (pageTitle: string) => {
    if (!props?.chatUiRef || !messageApiToUse) {
      console.error('UI交互模式需要提供chatUiRef和messageApi参数')
      return
    }

    const currentTab = await getCurrentTab(messageApiToUse)
    if (!currentTab?.id) {
      messageApiToUse.error('无法获取当前标签页')
      return
    }

    try {
      // 记录日志
      reportLog(pageTitle)

      // 1. 创建新会话
      if (props.chatUiRef.current?.chatContext?.handleNewConversation) {
        await props.chatUiRef.current.chatContext.handleNewConversation()
        console.log('新会话创建成功')
      }

      // 2. 发送翻译页面的消息
      const translateMessage = `请翻译当前页面的内容。页面标题：${currentTab.title || '未知页面'}`

      if (props.chatUiRef.current?.chatContext?.onSend) {
        // 使用chatUI的onSend方法发送消息
        await props.chatUiRef.current.chatContext.onSend({
          type: 'text',
          content: {
            text: translateMessage
          },
          agentId: 'translate' // 指定翻译智能体
        })
        console.log('翻译消息发送成功:', translateMessage)
      }

      // 3. 同时向content script发送开始翻译的消息
      await chrome.tabs.sendMessage(currentTab.id, {
        type: MessageType.START_TRANSLATE,
        data: { pageTitle }
      })

      messageApiToUse.success('翻译请求已发送')

    } catch (error) {
      console.error('翻译处理失败:', error)
      messageApiToUse.error('翻译处理失败，请重试')
    }

  }, [props?.chatUiRef, messageApiToUse, reportLog, refreshCurrentPage, getCurrentTab])

  // 初始化网络连接检查
  useEffect(() => {
    ipConnectivity(messageApiToUse)
  }, [messageApiToUse])

  return {
    contextHolder,
    handleTranslate
  }
}

// 导出类型和hook
export type { UseTranslateProps, UseTranslateReturn }
export default useTranslate
