/**
 * 通过chrome-extension://<extension-id>/tabs/settings.html 访问
 */
import React, { useState, useEffect } from 'react'
import {
  Button,
  Card,
  Avatar,
  Switch,
  Modal,
  Input,
  message,
  Segmented,
  Select,
} from 'antd/es'
import { DeleteOutlined, PlusOutlined } from '@ant-design/icons'
import { log } from '@ht/xlog'
import {
  getAllConfig,
  getStorageData,
  setStorageData,
  STORAGE_KEYS,
  userStorage,
} from '@src/common/utils'
import HoverImg from '../../assets/hover.svg'
import TransImg from '../../assets/trans.svg'
import logo from '../../assets/logo.png'
import './settings.less'

interface DisabledWebsite {
  id: string
  domain: string
}

type SelectionMode = 'read' | 'edit'

export default () => {
  const [messageApi, contextHolder] = message.useMessage()
  const [activeTab, setActiveTab] = useState('account') // 'account' | 'floating' | 'selection' | 'translation'
  const [alwaysFloat, setAlwaysFloat] = useState(true)
  const [disabledWebsites, setDisabledWebsites] = useState<DisabledWebsite[]>(
    []
  )
  const [isModalVisible, setIsModalVisible] = useState(false)
  const [newDomain, setNewDomain] = useState('')
  const [userInfo, setUserInfo] = useState<any>({})

  // 划选工具设置
  const [selectionAlwaysShow, setSelectionAlwaysShow] = useState<boolean>(true)
  const [selectionMode, setSelectionMode] = useState<SelectionMode>('read')

  // 翻译设置
  const [
    translationDefaultTargetLanguage,
    setTranslationDefaultTargetLanguage,
  ] = useState<string>('zh')

  const reportLog = (pageTitle: string) => {
    log({
      id: 'button_click',
      page_id: 'settings_tab',
      page_title: pageTitle,
    })
  }

  // 加载存储的设置
  useEffect(() => {
    loadSettings()

    // 在开发环境中运行测试
    getAllConfig().then((res) => {
      console.log('res', res)
    })
  }, [])

  const loadSettings = async () => {
    try {
      const result = await getStorageData([
        STORAGE_KEYS.ALWAYS_FLOAT,
        STORAGE_KEYS.DISABLED_WEBSITES,
        STORAGE_KEYS.SELECTION_ALWAYS_SHOW,
        STORAGE_KEYS.SELECTION_MODE,
        STORAGE_KEYS.TRANSLATION_DEFAULT_TARGET_LANGUAGE,
      ])

      const userInfo = await userStorage.getUserInfo()

      setAlwaysFloat(result[STORAGE_KEYS.ALWAYS_FLOAT] !== false) // 默认为true
      setDisabledWebsites(result[STORAGE_KEYS.DISABLED_WEBSITES] || [])
      setSelectionAlwaysShow(
        result[STORAGE_KEYS.SELECTION_ALWAYS_SHOW] !== false
      )
      setSelectionMode(
        (result[STORAGE_KEYS.SELECTION_MODE] as SelectionMode) || 'read'
      )
      setTranslationDefaultTargetLanguage(
        result[STORAGE_KEYS.TRANSLATION_DEFAULT_TARGET_LANGUAGE] || 'zh'
      )

      setUserInfo(userInfo)
    } catch (error) {
      console.error('加载设置失败:', error)
    }
  }

  const saveSettings = async (key: string, value: any) => {
    try {
      await setStorageData({ [key]: value })
    } catch (error) {
      console.error('保存设置失败:', error)
      messageApi.open({
        type: 'error',
        content: '保存设置失败，请重试',
      })
    }
  }

  const handleLogout = () => {
    reportLog('退出登录')
    userStorage.clearUserInfo()
  }

  const handleNavItemClick = (itemName: string, tabKey: string) => {
    reportLog(`点击${itemName}`)
    setActiveTab(tabKey)
  }

  const handleAlwaysFloatChange = (checked: boolean) => {
    setAlwaysFloat(checked)
    saveSettings(STORAGE_KEYS.ALWAYS_FLOAT, checked)
    reportLog(`切换始终悬浮: ${checked}`)
  }

  const handleAddDisabledWebsite = () => {
    setIsModalVisible(true)
    setNewDomain('')
  }

  const handleModalOk = () => {
    if (!newDomain.trim()) {
      messageApi.open({
        type: 'error',
        content: '请输入域名',
      })
      return
    }

    // 允许输入完整URL，自动提取域名
    let input = newDomain.trim().toLowerCase()
    let domain = input
    if (input.includes('://')) {
      try {
        const url = new URL(input)
        domain = url.hostname
      } catch (e) {
        messageApi.open({ type: 'error', content: '请输入有效的URL或域名' })
        return
      }
    }

    // 简单的域名验证
    if (
      !/^([a-zA-Z0-9][a-zA-Z0-9-]{0,61}[a-zA-Z0-9])(\.[a-zA-Z0-9][a-zA-Z0-9-]{0,61}[a-zA-Z0-9])+$/i.test(
        domain
      )
    ) {
      messageApi.open({
        type: 'error',
        content: '请输入有效的域名',
      })
      return
    }

    // 检查是否已存在
    if (disabledWebsites.some((site) => site.domain === domain)) {
      messageApi.open({
        type: 'error',
        content: '该域名已存在',
      })
      return
    }

    const newWebsite: DisabledWebsite = {
      id: Date.now().toString(),
      domain: domain,
    }

    const updatedWebsites = [...disabledWebsites, newWebsite]
    setDisabledWebsites(updatedWebsites)
    saveSettings(STORAGE_KEYS.DISABLED_WEBSITES, updatedWebsites)
    setIsModalVisible(false)
    setNewDomain('')

    messageApi.open({
      type: 'success',
      content: '添加成功',
    })

    reportLog('添加禁用网站')
  }

  const handleModalCancel = () => {
    setIsModalVisible(false)
    setNewDomain('')
  }

  const handleDeleteWebsite = (id: string) => {
    const updatedWebsites = disabledWebsites.filter((site) => site.id !== id)
    setDisabledWebsites(updatedWebsites)
    saveSettings(STORAGE_KEYS.DISABLED_WEBSITES, updatedWebsites)

    messageApi.open({
      type: 'success',
      content: '删除成功',
    })

    reportLog('删除禁用网站')
  }

  const renderAccountTab = () => (
    <div className="main-content">
      <h1 className="settings-title">我的账号</h1>

      <Card className="user-profile-card">
        <div className="user-info">
          <Avatar
            size={64}
            src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjQiIGhlaWdodD0iNjQiIHZpZXdCb3g9IjAgMCA2NCA2NCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjY0IiBoZWlnaHQ9IjY0IiBmaWxsPSIjMTk3NkQyIi8+CjxwYXRoIGQ9Ik0zMiAxNkMyMy4xNjM0IDE2IDE2IDIzLjE2MzQgMTYgMzJDMzIgMzIgMzIgMzIgMzIgMzJDMzIgMjMuMTYzNCAyNC44MzY2IDE2IDE2IDE2WiIgZmlsbD0id2hpdGUiLz4KPHBhdGggZD0iTTMyIDQ4QzQwLjgzNjYgNDggNDggNDAuODM2NiA0OCAzMkM0OCAyMy4xNjM0IDQwLjgzNjYgMTYgMzIgMTZDMjMuMTYzNCAxNiAxNiAyMy4xNjM0IDE2IDMyQzE2IDQwLjgzNjYgMjMuMTYzNCA0OCAzMiA0OFoiIGZpbGw9IndoaXRlIi8+Cjwvc3ZnPgo="
            className="user-avatar"
          />
          <div className="user-details">
            <div className="user-name">{userInfo.userName}</div>
            <div className="user-id">{userInfo.userId}</div>
          </div>
        </div>
      </Card>

      <div className="logout-section">
        <Button
          type="primary"
          size="large"
          onClick={handleLogout}
          className="logout-button"
        >
          退出登录
        </Button>
      </div>
    </div>
  )

  const renderFloatingTab = () => (
    <div className="main-content">
      <h1 className="settings-title">悬浮窗设置</h1>

      {/* 预览区域 */}
      <div className="floating-preview">
        <img src={HoverImg} alt="悬浮窗预览" />
      </div>

      {/* 设置分组 */}
      <div className="settings-section">
        {/* 始终悬浮设置 */}
        <div className="setting-item">
          <div className="setting-label">始终悬浮在页面侧边</div>
          <Switch
            checked={alwaysFloat}
            onChange={handleAlwaysFloatChange}
            className="setting-switch"
          />
        </div>
      </div>

      {/* 禁用网站设置 */}
      <div className="setting-item">
        <div className="setting-label">禁用的网站</div>
        <Button
          type="primary"
          icon={<PlusOutlined />}
          onClick={handleAddDisabledWebsite}
          className="add-button"
        >
          添加禁用网站
        </Button>
      </div>
      {/* 禁用网站列表 */}
      {disabledWebsites.length > 0 && (
        <div className="disabled-websites-list">
          {disabledWebsites.map((website) => (
            <div key={website.id} className="website-item">
              <span className="website-domain">{website.domain}</span>
              <Button
                type="text"
                icon={<DeleteOutlined />}
                onClick={() => handleDeleteWebsite(website.id)}
                className="delete-button"
              >
                删除
              </Button>
            </div>
          ))}
        </div>
      )}

      {/* 添加禁用网站弹窗 */}
      <Modal
        title="添加禁用网站"
        open={isModalVisible}
        onOk={handleModalOk}
        onCancel={handleModalCancel}
        okText="添加"
        cancelText="取消"
        className="add-website-modal"
      >
        <div className="modal-description">
          输入要禁用悬浮窗的网站域名，例如：example.com
        </div>
        <Input
          placeholder="请输入域名"
          value={newDomain}
          onChange={(e) => setNewDomain(e.target.value)}
          className="domain-input"
          onPressEnter={handleModalOk}
        />
      </Modal>
    </div>
  )

  const renderSelectionTab = () => (
    <div className="main-content">
      <h1 className="settings-title">划选工具</h1>
      <Segmented
        options={[
          { label: '在阅读网页时', value: 'read' },
          { label: '在编辑文本时', value: 'edit' },
        ]}
        value={selectionMode}
        size="middle"
        onChange={(val) => {
          const v = val as SelectionMode
          setSelectionMode(v)
          saveSettings(STORAGE_KEYS.SELECTION_MODE, v)
          reportLog(`划选工具-场景: ${v}`)
        }}
      />
      <div className="selection-image">
        <img src={TransImg} alt="划选工具示意图" />
      </div>
      <div className="settings-section">
        <div className="setting-item">
          <div className="setting-label">始终在选中网页文字时出现</div>
          <Switch
            checked={selectionAlwaysShow}
            onChange={(checked) => {
              setSelectionAlwaysShow(checked)
              saveSettings(STORAGE_KEYS.SELECTION_ALWAYS_SHOW, checked)
              reportLog(`划选工具-始终显示: ${checked}`)
            }}
          />
        </div>
      </div>
    </div>
  )

  const renderTranslationTab = () => (
    <div className="main-content">
      <h1 className="settings-title">翻译设置</h1>
      <div className="settings-section">
        <div className="setting-item">
          <div className="setting-label">默认目标语言</div>
          <Select
            className="language-select"
            style={{ width: '200px' }}
            value={translationDefaultTargetLanguage}
            onChange={(value) => {
              setTranslationDefaultTargetLanguage(value)
              saveSettings(
                STORAGE_KEYS.TRANSLATION_DEFAULT_TARGET_LANGUAGE,
                value
              )
              reportLog(`翻译设置-默认目标语言: ${value}`)
            }}
            placeholder="请选择默认目标语言"
          >
            <Select.Option value="中文 (Chinese)">中文 (Chinese)</Select.Option>
            <Select.Option value="English (英语)">English (英语)</Select.Option>
            <Select.Option value="日本語 (Japanese)">
              日本語 (Japanese)
            </Select.Option>
            <Select.Option value="한국어 (Korean)">
              한국어 (Korean)
            </Select.Option>
            <Select.Option value="Français (French)">
              Français (French)
            </Select.Option>
            <Select.Option value="Deutsch (German)">
              Deutsch (German)
            </Select.Option>
            <Select.Option value="Español (Spanish)">
              Español (Spanish)
            </Select.Option>
            <Select.Option value="Português (Portuguese)">
              Português (Portuguese)
            </Select.Option>
            <Select.Option value="Italiano (Italian)">
              Italiano (Italian)
            </Select.Option>
            <Select.Option value="Русский (Russian)">
              Русский (Russian)
            </Select.Option>
            <Select.Option value="العربية (Arabic)">
              العربية (Arabic)
            </Select.Option>
            <Select.Option value="हिन्दी (Hindi)">हिन्दी (Hindi)</Select.Option>
            <Select.Option value="ไทย (Thai)">ไทย (Thai)</Select.Option>
            <Select.Option value="Tiếng Việt (Vietnamese)">
              Tiếng Việt (Vietnamese)
            </Select.Option>
            <Select.Option value="Bahasa Indonesia (Indonesian)">
              Bahasa Indonesia (Indonesian)
            </Select.Option>
            <Select.Option value="Bahasa Melayu (Malay)">
              Bahasa Melayu (Malay)
            </Select.Option>
            <Select.Option value="Türkçe (Turkish)">
              Türkçe (Turkish)
            </Select.Option>
            <Select.Option value="Polski (Polish)">
              Polski (Polish)
            </Select.Option>
            <Select.Option value="Nederlands (Dutch)">
              Nederlands (Dutch)
            </Select.Option>
            <Select.Option value="Svenska (Swedish)">
              Svenska (Swedish)
            </Select.Option>
            <Select.Option value="Dansk (Danish)">Dansk (Danish)</Select.Option>
            <Select.Option value="Norsk (Norwegian)">
              Norsk (Norwegian)
            </Select.Option>
            <Select.Option value="Suomi (Finnish)">
              Suomi (Finnish)
            </Select.Option>
            <Select.Option value="Čeština (Czech)">
              Čeština (Czech)
            </Select.Option>
            <Select.Option value="Magyar (Hungarian)">
              Magyar (Hungarian)
            </Select.Option>
            <Select.Option value="Română (Romanian)">
              Română (Romanian)
            </Select.Option>
            <Select.Option value="Български (Bulgarian)">
              Български (Bulgarian)
            </Select.Option>
            <Select.Option value="Hrvatski (Croatian)">
              Hrvatski (Croatian)
            </Select.Option>
            <Select.Option value="Slovenčina (Slovak)">
              Slovenčina (Slovak)
            </Select.Option>
            <Select.Option value="Slovenščina (Slovenian)">
              Slovenščina (Slovenian)
            </Select.Option>
            <Select.Option value="Eesti (Estonian)">
              Eesti (Estonian)
            </Select.Option>
            <Select.Option value="Latviešu (Latvian)">
              Latviešu (Latvian)
            </Select.Option>
            <Select.Option value="Lietuvių (Lithuanian)">
              Lietuvių (Lithuanian)
            </Select.Option>
            <Select.Option value="Malti (Maltese)">
              Malti (Maltese)
            </Select.Option>
            <Select.Option value="Ελληνικά (Greek)">
              Ελληνικά (Greek)
            </Select.Option>
            <Select.Option value="עברית (Hebrew)">עברית (Hebrew)</Select.Option>
            <Select.Option value="فارسی (Persian)">
              فارسی (Persian)
            </Select.Option>
            <Select.Option value="اردو (Urdu)">اردو (Urdu)</Select.Option>
            <Select.Option value="বাংলা (Bengali)">
              বাংলা (Bengali)
            </Select.Option>
            <Select.Option value="தமிழ் (Tamil)">தமிழ் (Tamil)</Select.Option>
            <Select.Option value="తెలుగు (Telugu)">
              తెలుగు (Telugu)
            </Select.Option>
            <Select.Option value="മലയാളം (Malayalam)">
              മലയാളം (Malayalam)
            </Select.Option>
            <Select.Option value="ಕನ್ನಡ (Kannada)">
              ಕನ್ನಡ (Kannada)
            </Select.Option>
            <Select.Option value="ગુજરાતી (Gujarati)">
              ગુજરાતી (Gujarati)
            </Select.Option>
            <Select.Option value="ਪੰਜਾਬੀ (Punjabi)">
              ਪੰਜਾਬੀ (Punjabi)
            </Select.Option>
            <Select.Option value="ଓଡ଼ିଆ (Odia)">ଓଡ଼ିଆ (Odia)</Select.Option>
            <Select.Option value="অসমীয়া (Assamese)">
              অসমীয়া (Assamese)
            </Select.Option>
            <Select.Option value="नेपाली (Nepali)">
              नेपाली (Nepali)
            </Select.Option>
            <Select.Option value="සිංහල (Sinhala)">
              සිංහල (Sinhala)
            </Select.Option>
            <Select.Option value="မြန်မာ (Burmese)">
              မြန်မာ (Burmese)
            </Select.Option>
            <Select.Option value="ខ្មែរ (Khmer)">ខ្មែរ (Khmer)</Select.Option>
            <Select.Option value="ລາວ (Lao)">ລາວ (Lao)</Select.Option>
            <Select.Option value="ქართული (Georgian)">
              ქართული (Georgian)
            </Select.Option>
            <Select.Option value="አማርኛ (Amharic)">አማርኛ (Amharic)</Select.Option>
            <Select.Option value="Kiswahili (Swahili)">
              Kiswahili (Swahili)
            </Select.Option>
            <Select.Option value="isiZulu (Zulu)">isiZulu (Zulu)</Select.Option>
            <Select.Option value="Afrikaans">Afrikaans</Select.Option>
            <Select.Option value="Shqip (Albanian)">
              Shqip (Albanian)
            </Select.Option>
            <Select.Option value="Македонски (Macedonian)">
              Македонски (Macedonian)
            </Select.Option>
            <Select.Option value="Српски (Serbian)">
              Српски (Serbian)
            </Select.Option>
            <Select.Option value="Bosanski (Bosnian)">
              Bosanski (Bosnian)
            </Select.Option>
            <Select.Option value="Crnogorski (Montenegrin)">
              Crnogorski (Montenegrin)
            </Select.Option>
            <Select.Option value="Íslenska (Icelandic)">
              Íslenska (Icelandic)
            </Select.Option>
            <Select.Option value="Føroyskt (Faroese)">
              Føroyskt (Faroese)
            </Select.Option>
            <Select.Option value="Galego (Galician)">
              Galego (Galician)
            </Select.Option>
            <Select.Option value="Euskara (Basque)">
              Euskara (Basque)
            </Select.Option>
            <Select.Option value="Català (Catalan)">
              Català (Catalan)
            </Select.Option>
            <Select.Option value="Cymraeg (Welsh)">
              Cymraeg (Welsh)
            </Select.Option>
            <Select.Option value="Gaeilge (Irish)">
              Gaeilge (Irish)
            </Select.Option>
            <Select.Option value="Gàidhlig (Scottish Gaelic)">
              Gàidhlig (Scottish Gaelic)
            </Select.Option>
            <Select.Option value="Kernewek (Cornish)">
              Kernewek (Cornish)
            </Select.Option>
            <Select.Option value="Brezhoneg (Breton)">
              Brezhoneg (Breton)
            </Select.Option>
            <Select.Option value="Occitan">Occitan</Select.Option>
            <Select.Option value="Corsu (Corsican)">
              Corsu (Corsican)
            </Select.Option>
            <Select.Option value="Lëtzebuergesch (Luxembourgish)">
              Lëtzebuergesch (Luxembourgish)
            </Select.Option>
            <Select.Option value="Rumantsch (Romansh)">
              Rumantsch (Romansh)
            </Select.Option>
            <Select.Option value="Furlan (Friulian)">
              Furlan (Friulian)
            </Select.Option>
            <Select.Option value="Sardu (Sardinian)">
              Sardu (Sardinian)
            </Select.Option>
            <Select.Option value="Vèneto (Venetian)">
              Vèneto (Venetian)
            </Select.Option>
            <Select.Option value="Nnapulitano (Neapolitan)">
              Nnapulitano (Neapolitan)
            </Select.Option>
            <Select.Option value="Sicilianu (Sicilian)">
              Sicilianu (Sicilian)
            </Select.Option>
            <Select.Option value="Piemontèis (Piedmontese)">
              Piemontèis (Piedmontese)
            </Select.Option>
            <Select.Option value="Lombard">Lombard</Select.Option>
            <Select.Option value="Emiliàn (Emilian)">
              Emiliàn (Emilian)
            </Select.Option>
            <Select.Option value="Ligure (Ligurian)">
              Ligure (Ligurian)
            </Select.Option>
            <Select.Option value="Picard">Picard</Select.Option>
            <Select.Option value="Walon (Walloon)">
              Walon (Walloon)
            </Select.Option>
            <Select.Option value="Arpitan (Franco-Provençal)">
              Arpitan (Franco-Provençal)
            </Select.Option>
            <Select.Option value="Alemannisch (Alemannic German)">
              Alemannisch (Alemannic German)
            </Select.Option>
            <Select.Option value="Bairisch (Bavarian German)">
              Bairisch (Bavarian German)
            </Select.Option>
            <Select.Option value="Ripoarisch (Ripuarian)">
              Ripoarisch (Ripuarian)
            </Select.Option>
            <Select.Option value="Plattdüütsch (Low German)">
              Plattdüütsch (Low German)
            </Select.Option>
            <Select.Option value="Frysk (West Frisian)">
              Frysk (West Frisian)
            </Select.Option>
            <Select.Option value="Seeltersk (Saterland Frisian)">
              Seeltersk (Saterland Frisian)
            </Select.Option>
            <Select.Option value="Deitsch (Pennsylvania German)">
              Deitsch (Pennsylvania German)
            </Select.Option>
            <Select.Option value="Pälzisch (Palatine German)">
              Pälzisch (Palatine German)
            </Select.Option>
            <Select.Option value="Schwäbisch (Swabian German)">
              Schwäbisch (Swabian German)
            </Select.Option>
          </Select>
        </div>
      </div>
    </div>
  )

  return (
    <div className="settings-tab-page">
      {contextHolder}

      {/* 左侧导航栏 */}
      <div className="settings-sidebar">
        {/* Logo */}
        <div className="sidebar-logo">
          <img src={logo} alt="logo" />
        </div>

        {/* 导航菜单 */}
        <div className="sidebar-nav">
          <div
            className={`nav-item ${activeTab === 'account' ? 'active' : ''}`}
            onClick={() => handleNavItemClick('我的账号', 'account')}
          >
            <div className="nav-icon">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                version="1.1"
                width="15"
                height="16"
                viewBox="0 3 15 16"
              >
                <g>
                  <g>
                    <path
                      d="M14.1114,15.6875C13.7422,14.8125,13.211,14.0273,12.5371,13.3535C11.8633,12.67969,11.0782,12.15039,10.2032,11.7793C10.1954,11.77539,10.1875,11.77344,10.1797,11.76953C11.3965,10.89063,12.1875,9.45898,12.1875,7.84375C12.1875,5.16797,10.0196,3,7.34379,3C4.66801,3,2.50004,5.16797,2.50004,7.84375C2.50004,9.45898,3.29106,10.89063,4.50785,11.77148C4.50004,11.77539,4.49223,11.77734,4.48441,11.78125C3.60941,12.15039,2.82426,12.67969,2.15043,13.3555C1.4766,14.0293,0.947305,14.8145,0.576212,15.6895C0.21293,16.5449,0.0195709,17.4531,0.**********,18.386699999999998C-0.********,18.***************,0.068399,18.5469,0.15629,18.5469L1.32816,18.5469C1.4141,18.5469,1.48246,18.4785,1.48441,18.3945C1.52348,16.886699999999998,2.12895,15.4746,3.19926,14.4043C4.30668,13.2969,5.77738,12.6875,7.34379,12.6875C8.9102,12.6875,10.3809,13.2969,11.4883,14.4043C12.5586,15.4746,13.1641,16.886699999999998,13.2032,18.3945C13.2051,18.4805,13.2735,18.5469,13.3594,18.5469L14.5313,18.5469C14.6192,18.5469,14.6895,18.***************,14.6875,18.386699999999998C14.668,17.4531,14.4746,16.5449,14.1114,15.6875ZM7.34379,11.20312C6.44731,11.20312,5.60356,10.85352,4.96879,10.21875C4.33402,9.58398,3.98441,8.74023,3.98441,7.84375C3.98441,6.94727,4.33402,6.10352,4.96879,5.46875C5.60356,4.83398,6.44731,4.48438,7.34379,4.48438C8.24027,4.48438,9.08402,4.83398,9.71879,5.46875C10.3536,6.10352,10.7032,6.94727,10.7032,7.84375C10.7032,8.74023,10.3536,9.58398,9.71879,10.21875C9.08402,10.85352,8.24027,11.20312,7.34379,11.20312Z"
                      fill="#333333"
                      fill-opacity="1"
                    />
                  </g>
                </g>
              </svg>
            </div>
            <span className="nav-text">我的账号</span>
          </div>

          <div
            className={`nav-item ${activeTab === 'floating' ? 'active' : ''}`}
            onClick={() => handleNavItemClick('悬浮窗设置', 'floating')}
          >
            <div className="nav-icon">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                version="1.1"
                width="17"
                height="17"
                viewBox="0 0 17 17"
              >
                <defs>
                  <clipPath id="master_svg0_348_20434">
                    <rect x="0" y="0" width="17" height="17" rx="0" />
                  </clipPath>
                </defs>
                <g clip-path="url(#master_svg0_348_20434)">
                  <g>
                    <path
                      d="M13.457,15.4045C17.2697,12.6677,18.1413,7.35669,15.4045,3.54398C12.6667,-0.269715,7.35668,-1.14133,3.54299,1.59549C-0.269715,4.33329,-1.14133,9.64431,1.59549,13.457C4.33329,17.2707,9.64431,18.1423,13.457,15.4045ZM4.36998,2.74673C7.54707,0.465061,11.9736,1.1919,14.2552,4.36998C16.5359,7.54806,15.8101,11.9736,12.632,14.2553C9.45392,16.5359,5.02841,15.8101,2.74773,12.632C0.465061,9.45392,1.1919,5.02742,4.36998,2.74673ZM5.13153,11.5006C5.667,11.1277,5.79789,10.392,5.42604,9.8575C5.0532,9.32204,4.31743,9.19115,3.78296,9.563C3.77403,9.56994,3.76412,9.57589,3.75519,9.58283C3.21973,9.95567,3.08884,10.6914,3.46069,11.2259C3.83353,11.7614,4.5693,11.8923,5.10377,11.5204C5.11368,11.5145,5.12261,11.5075,5.13153,11.5006ZM5.01353,8.09741C5.29911,7.89116,5.4776,7.5669,5.49743,7.21489C5.71459,6.66752,6.0686,6.17767,6.57233,5.81574C7.07606,5.4548,7.65317,5.27532,8.24119,5.24458C8.5704,5.33283,8.93333,5.28226,9.23279,5.06807C9.76231,4.68829,9.88328,3.95054,9.5035,3.42102Q9.5035,3.42003,9.50251,3.42003Q9.17925,2.97084,8.85003,2.88754C7.58178,2.78937,6.28972,3.11065,5.194,3.89699C4.09829,4.68333,3.38136,5.80483,3.069,7.03838Q3.04223,7.37652,3.36549,7.8267C3.74627,8.35622,4.48402,8.47719,5.01353,8.09741Z"
                      fill="#000000"
                      fill-opacity="1"
                      style={{ mixBlendMode: 'passthrough' as any }}
                    />
                  </g>
                </g>
              </svg>
            </div>
            <span className="nav-text">悬浮窗设置</span>
          </div>

          <div
            className={`nav-item ${activeTab === 'selection' ? 'active' : ''}`}
            onClick={() => handleNavItemClick('划选工具', 'selection')}
          >
            <div className="nav-icon">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                version="1.1"
                width="22"
                height="22"
                viewBox="0 0 22 22"
              >
                <defs>
                  <clipPath id="master_svg0_348_20423">
                    <rect x="0" y="0" width="22" height="22" rx="0" />
                  </clipPath>
                </defs>
                <g clip-path="url(#master_svg0_348_20423)">
                  <g>
                    <path
                      d="M18.383065625,6.0392423046875Q18.379765625,5.1534923046875,17.753865625,4.5267123046875L16.350865625,3.1215313046875Q15.712265625,2.4820633246875,14.808665625,2.4902149136875Q13.905065625,2.4983664846874998,13.278165625,3.1492493046875L2.294402625,14.5536123046875Q1.884765625,14.9790123046875,1.884765625,15.5695123046875L1.884765625,18.7345123046875C1.884765625,19.1636123046875,2.232584625,19.5114123046875,2.661640625,19.5114123046875L5.389175625,19.5114123046875Q6.002725625,19.5114123046875,6.433025625,19.0740123046875L17.765065625,7.5563723046875Q18.386265625,6.9249823046875,18.383065625,6.0392423046875ZM15.251365625,4.2193823046875L16.654465625,5.6245523046875Q17.073265624999998,6.0440723046875,16.657565625,6.4666723046875L15.263365625,7.8836423046875L13.028365625,5.6484023046875L14.397265625,4.2270823046874995Q14.571565625,4.0461723046875,14.822665625,4.0439023046875Q15.073865625,4.0416323046875,15.251365625,4.2193823046875ZM11.950365625,6.7677123046875L3.438515625,15.6055123046875L3.438515625,17.9576123046875L5.351735625,17.9576123046875L14.173665625,8.9912423046875L11.950365625,6.7677123046875ZM19.340765625,17.9569123046875L10.415545625,17.9569123046875C9.986485625,17.9569123046875,9.638675625000001,18.3047123046875,9.638675625000001,18.7338123046875C9.638675625000001,19.1628123046875,9.986485625,19.5106123046875,10.415545625,19.5106123046875L19.340765625,19.5106123046875C19.769765625,19.5106123046875,20.117665625,19.1628123046875,20.117665625,18.7338123046875C20.117665625,18.3047123046875,19.769765625,17.9569123046875,19.340765625,17.9569123046875Z"
                      fill-rule="evenodd"
                      fill="#1D222C"
                      fill-opacity="1"
                    />
                  </g>
                  <g>
                    <path
                      d="M1.0950061,4.136950000000001C1.034866,4.16312,0.99713119,4.2237,1.000170875,4.28922C0.9975761400000001,4.35435,1.0352373,4.4144000000000005,1.0950061,4.44042C2.06448,4.735390000000001,2.8231900000000003,5.4940999999999995,3.11816,6.46357C3.13783,6.53101,3.19964,6.57738,3.26989,6.57738C3.34029,6.57731,3.40215,6.53069,3.42163,6.46305C3.7164,5.493650000000001,4.4749300000000005,4.73493,5.44425,4.4399C5.51169,4.42023,5.55805,4.35841,5.55805,4.2881599999999995C5.55805,4.21791,5.51169,4.15609,5.44425,4.13643C4.4750499999999995,3.8415,3.71655,3.083,3.42163,2.113802C3.40196,2.0463638,3.34014,2,3.26989,2C3.19964,2,3.13783,2.0463638,3.11816,2.113802C2.8231900000000003,3.0832800000000002,2.06448,3.84199,1.0950061,4.136950000000001Z"
                      fill="#000000"
                      fill-opacity="1"
                      style={{ mixBlendMode: 'passthrough' as any }}
                    />
                  </g>
                </g>
              </svg>
            </div>
            <span className="nav-text">划选工具</span>
          </div>

          <div
            className={`nav-item ${
              activeTab === 'translation' ? 'active' : ''
            }`}
            onClick={() => handleNavItemClick('翻译设置', 'translation')}
          >
            <div className="nav-icon">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                version="1.1"
                width="21"
                height="21"
                viewBox="0 0 21 21"
              >
                <defs>
                  <clipPath id="master_svg0_352_18932">
                    <rect x="0" y="0" width="21" height="21" rx="0" />
                  </clipPath>
                </defs>
                <g>
                  <g clip-path="url(#master_svg0_352_18932)">
                    <g>
                      <path
                        d="M7.805296640625,17.164575L6.052806640625,17.164575L5.917436640625,17.160375000000002C4.955136640625,17.087975,4.202716640625,16.275675,4.202716640625,15.311275L4.202716640625,14.437175C4.202716640625,14.013175,3.857466640625,13.667975,3.433516640625,13.667975C3.009556640625,13.667975,2.664306640625,14.013175,2.664306640625,14.437175L2.664306640625,15.311275C2.664306640625,17.182375,4.185926640625,18.703975,6.056996640625,18.703975L7.806346640625,18.703975C8.230296640625,18.703975,8.574496640625,18.358775,8.575546640625,17.934775000000002C8.575546640625,17.510875,8.230296640625,17.165575,7.806346640625,17.164575L7.805296640625,17.164575ZM19.188706640625,18.246375L15.682706640625,9.481864999999999C15.628106640625,9.366425,15.512706640625,9.292964999999999,15.381506640625,9.292964999999999L14.225106640625,9.292964999999999C14.088606640625,9.292964999999999,13.968006640625,9.374825000000001,13.916506640625,9.501795000000001L10.418916640625,18.246375C10.403166640625,18.286275,10.394776640625,18.327175,10.394776640625,18.370275C10.394776640625,18.553875,10.543786640625,18.702875,10.727436640625,18.702875L11.668736640625,18.702875C11.805206640625,18.702875,11.926906640625,18.621075,11.977306640625,18.494075L12.941706640625,16.079375L16.660706640625,16.079375L17.627206640624998,18.494075C17.677606640625,18.621075,17.799306640625,18.702875,17.935706640625,18.702875L18.879106640625,18.702875C18.922206640625,18.702875,18.963106640625,18.694475,19.003006640625,18.678775C19.173006640625,18.610575,19.255906640625,18.416375,19.187606640625,18.246375L19.188706640625,18.246375ZM13.557606640625,14.539975L14.803306640625,11.428485L16.046806640625,14.539975L13.557606640625,14.539975ZM9.992426640625,4.046215L6.826406640625,4.046215L6.826406640625,2.629533C6.826406640625,2.445889,6.677386640625,2.296875,6.493746640625,2.296875L5.619596640625,2.296875C5.435956640625,2.296875,5.286936640625,2.445889,5.286936640625,2.629533L5.286936640625,4.046215L2.121964640625,4.046215C1.938320640625,4.046215,1.789306640625,4.195235,1.789306640625,4.378875L1.789306640625,9.625845C1.789306640625,9.809495,1.938320640625,9.958504999999999,2.121964640625,9.958504999999999L5.287986640625,9.958504999999999L5.287986640625,12.249335C5.287986640625,12.432975,5.437006640625,12.581975,5.620646640625,12.581975L6.494796640625,12.581975C6.678436640625,12.581975,6.827456640625,12.432975,6.827456640625,12.249335L6.827456640625,9.958504999999999L9.993476640625,9.958504999999999C10.177116640625,9.958504999999999,10.326136640625,9.809495,10.326136640625,9.625845L10.326136640625,4.377825C10.326136640625,4.194185,10.177116640625,4.045165,9.993476640625,4.045165L9.992426640625,4.046215ZM5.286936640625,8.417995000000001L3.328766640625,8.417995000000001L3.328766640625,5.584625L5.287986640625,5.584625L5.287986640625,8.417995000000001L5.286936640625,8.417995000000001ZM8.784576640625,8.417995000000001L6.825356640625,8.417995000000001L6.825356640625,5.584625L8.784576640625,5.584625L8.784576640625,8.417995000000001ZM13.************,4.710285L14.802006640625,4.710285C15.824106640625,4.710285,16.656306640625,5.542455,16.656306640625,6.564565L16.656306640625,7.438715C16.656306640625,7.862675,17.001506640625,8.206875,17.425506640625002,8.207925C17.849406640625,8.207925,18.194706640625,7.862675,18.194706640625,7.438715L18.194706640625,6.564565C18.194706640625,4.693495,16.673106640625,3.171875,14.802006640625,3.171875L13.************,3.171875C12.************,3.171875,12.************,3.517125,12.************,3.941085C12.************,4.***************,12.************,4.710285,13.************,4.710285Z"
                        fill="#1D222C"
                        fill-opacity="1"
                      />
                    </g>
                  </g>
                </g>
              </svg>
            </div>
            <span className="nav-text">翻译设置</span>
          </div>
        </div>
      </div>

      {/* 右侧主内容区 */}
      <div className="settings-main">
        {activeTab === 'account' && renderAccountTab()}
        {activeTab === 'floating' && renderFloatingTab()}
        {activeTab === 'selection' && renderSelectionTab()}
        {activeTab === 'translation' && renderTranslationTab()}
      </div>
    </div>
  )
}
